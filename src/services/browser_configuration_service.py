"""Service for managing browser configurations and session pools."""

import logging
from typing import Dict, Any, <PERSON>, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path

from ..database.repositories.browser_configuration_repository import (
    BrowserConfigurationRepository,
    BrowserSessionPoolRepository
)
from ..database.models.browser_configuration import BrowserConfiguration, BrowserSessionPool
from ..config.browser_config import BrowserConfigurations, get_config_by_type
from ..utilities.browser_helper import BrowserHelperConfig, validate_config
from .base_service import BaseService, DatabaseServiceMixin, ServiceResult

logger = logging.getLogger(__name__)


class BrowserConfigurationService(BaseService, DatabaseServiceMixin):
    """Service for managing browser configurations."""
    
    def __init__(self):
        super().__init__("browser_configurations")
        self._config_repo: Optional[BrowserConfigurationRepository] = None
        self._session_repo: Optional[BrowserSessionPoolRepository] = None
    
    @property
    def config_repo(self) -> BrowserConfigurationRepository:
        if self._config_repo is None:
            self._config_repo = BrowserConfigurationRepository()
        return self._config_repo
    
    @property
    def session_repo(self) -> BrowserSessionPoolRepository:
        if self._session_repo is None:
            self._session_repo = BrowserSessionPoolRepository()
        return self._session_repo
    
    # Configuration Management
    
    async def create_configuration(
        self,
        name: str,
        settings: Dict[str, Any],
        description: Optional[str] = None,
        config_type: str = "custom",
        created_by: Optional[str] = None,
        project_id: Optional[str] = None,
        suite_id: Optional[str] = None,
        tags: Optional[List[str]] = None,
        execution_types: Optional[List[str]] = None,
        is_default: bool = False
    ) -> Tuple[BrowserConfiguration, List[str]]:
        """Create a new browser configuration.
        
        Returns:
            Tuple of (configuration, validation_warnings)
        """
        # Validate configuration
        try:
            browser_config = BrowserHelperConfig(**settings)
            warnings = validate_config(browser_config)
        except Exception as e:
            raise ValueError(f"Invalid configuration settings: {str(e)}")
        
        # Validate and set execution types
        from ..database.models.browser_configuration import ExecutionType
        valid_execution_types = []
        if execution_types:
            for exec_type in execution_types:
                try:
                    valid_execution_types.append(ExecutionType(exec_type.lower()))
                except ValueError:
                    logger.warning(f"Invalid execution type: {exec_type}")
        
        # Use default execution types if none provided or all invalid
        if not valid_execution_types:
            valid_execution_types = [ExecutionType.SMOKE, ExecutionType.FULL, ExecutionType.CASE, ExecutionType.SUITE, ExecutionType.CODEGEN]
        
        # Create configuration document
        config = BrowserConfiguration(
            name=name,
            description=description,
            config_type=config_type,
            settings=settings,
            execution_types=valid_execution_types,
            created_by=created_by,
            project_id=project_id,
            suite_id=suite_id,
            tags=tags or [],
            is_default=is_default,
            validation_warnings=warnings,
            is_valid=len(warnings) == 0
        )
        
        await config.save()
        logger.info(f"Created browser configuration: {config.config_id} - {name}")
        
        return config, warnings
    
    async def update_configuration(
        self,
        config_id: str,
        name: Optional[str] = None,
        settings: Optional[Dict[str, Any]] = None,
        description: Optional[str] = None,
        tags: Optional[List[str]] = None,
        execution_types: Optional[List[str]] = None,
        is_default: Optional[bool] = None
    ) -> Tuple[BrowserConfiguration, List[str]]:
        """Update an existing configuration.
        
        Returns:
            Tuple of (configuration, validation_warnings)
        """
        config = await self.config_repo.get_by_config_id(config_id)
        if not config:
            raise ValueError(f"Configuration not found: {config_id}")
        
        # Update fields
        if name is not None:
            config.name = name
        if description is not None:
            config.description = description
        if tags is not None:
            config.tags = tags
        if is_default is not None:
            config.is_default = is_default
        
        # Update execution types if provided
        if execution_types is not None:
            from ..database.models.browser_configuration import ExecutionType
            valid_execution_types = []
            for exec_type in execution_types:
                try:
                    valid_execution_types.append(ExecutionType(exec_type.lower()))
                except ValueError:
                    logger.warning(f"Invalid execution type: {exec_type}")
            
            if valid_execution_types:
                config.execution_types = valid_execution_types
            else:
                logger.warning("No valid execution types provided, keeping existing ones")
        
        warnings = []
        if settings is not None:
            # Validate new settings
            try:
                browser_config = BrowserHelperConfig(**settings)
                warnings = validate_config(browser_config)
                config.settings = settings
                config.validation_warnings = warnings
                config.is_valid = len(warnings) == 0
            except Exception as e:
                raise ValueError(f"Invalid configuration settings: {str(e)}")
        
        config.updated_at = datetime.utcnow()
        await config.save()
        
        logger.info(f"Updated browser configuration: {config_id}")
        return config, warnings
    
    async def get_configuration(self, config_id: str) -> Optional[BrowserConfiguration]:
        """Get a configuration by ID."""
        return await self.config_repo.get_by_config_id(config_id)
    
    async def list_configurations(
        self,
        project_id: Optional[str] = None,
        suite_id: Optional[str] = None,
        config_type: Optional[str] = None,
        created_by: Optional[str] = None,
        tags: Optional[List[str]] = None,
        include_global: bool = True
    ) -> List[BrowserConfiguration]:
        """List configurations with optional filters."""
        if suite_id:
            return await self.config_repo.get_by_suite(suite_id, include_project=True, include_global=include_global)
        elif project_id:
            return await self.config_repo.get_by_project(project_id, include_global=include_global)
        elif config_type:
            return await self.config_repo.get_by_type(config_type)
        elif tags:
            return await self.config_repo.search_by_tags(tags)
        else:
            # Get all active configurations
            return await BrowserConfiguration.find({"is_active": True}).to_list()
    
    async def get_predefined_configurations(self) -> List[Dict[str, Any]]:
        """Get all predefined configurations."""
        predefined_configs = [
            {
                "config_type": "test_case",
                "name": "Test Case / Test Suite",
                "description": "Configuración para ejecución de casos de prueba específicos y suites de testing",
                "settings": self._browser_helper_config_to_dict(BrowserConfigurations.get_test_case_config())
            },
            {
                "config_type": "smoke",
                "name": "Smoke",
                "description": "Configuración para pruebas rápidas de funcionalidad básica - smoke testing",
                "settings": self._browser_helper_config_to_dict(BrowserConfigurations.get_smoke_config())
            },
            {
                "config_type": "exploration",
                "name": "Exploration",
                "description": "Configuración para exploración general de aplicaciones web",
                "settings": self._browser_helper_config_to_dict(BrowserConfigurations.get_exploration_config())
            },
            {
                "config_type": "exploration_deep",
                "name": "Exploration Deep",
                "description": "Configuración para exploración exhaustiva y detallada de aplicaciones complejas",
                "settings": self._browser_helper_config_to_dict(BrowserConfigurations.get_exploration_deep_config())
            }
        ]
        return predefined_configs
    
    async def delete_configuration(self, config_id: str) -> bool:
        """Delete (deactivate) a configuration."""
        return await self.config_repo.deactivate_config(config_id)
    
    async def clone_configuration(
        self,
        config_id: str,
        new_name: str,
        created_by: Optional[str] = None
    ) -> Optional[BrowserConfiguration]:
        """Clone an existing configuration."""
        return await self.config_repo.clone_config(config_id, new_name, created_by)
    
    async def increment_usage(self, config_id: str) -> bool:
        """Increment usage count for a configuration."""
        return await self.config_repo.increment_usage(config_id)
    
    # Session Pool Management for Test Suites
    
    async def create_session_pool(
        self,
        suite_id: str,
        project_id: str,
        config_id: str,
        session_id: str,
        browser_type: str = "chromium",
        user_data_dir: Optional[str] = None,
        cdp_url: Optional[str] = None,
        ws_endpoint: Optional[str] = None,
        expires_in_hours: int = 24
    ) -> BrowserSessionPool:
        """Create a new browser session pool for a test suite."""
        # Get configuration snapshot
        config = await self.get_configuration(config_id)
        configuration_snapshot = config.settings if config else {}
        
        # Calculate expiration
        expires_at = datetime.utcnow() + timedelta(hours=expires_in_hours)
        
        session_pool = BrowserSessionPool(
            session_id=session_id,
            suite_id=suite_id,
            project_id=project_id,
            config_id=config_id,
            configuration=configuration_snapshot,
            browser_type=browser_type,
            user_data_dir=user_data_dir,
            cdp_url=cdp_url,
            ws_endpoint=ws_endpoint,
            expires_at=expires_at
        )
        
        await session_pool.save()
        logger.info(f"Created session pool: {session_pool.pool_id} for suite: {suite_id}")
        
        return session_pool
    
    async def get_or_create_session_for_suite(
        self,
        suite_id: str,
        project_id: str,
        config_id: str,
        browser_type: str = "chromium"
    ) -> Optional[BrowserSessionPool]:
        """Get an existing session or create a new one for a test suite."""
        # Try to get an available session first
        session = await self.session_repo.get_available_for_suite(suite_id)
        
        if session:
            logger.info(f"Reusing existing session: {session.session_id} for suite: {suite_id}")
            return session
        
        # No available session, need to create a new one
        # This would typically involve starting a new browser instance
        # For now, we'll return None to indicate that a new browser session needs to be created
        logger.info(f"No available session found for suite: {suite_id}, new session needed")
        return None
    
    async def lock_session_for_test(self, suite_id: str, test_case_id: str) -> Optional[BrowserSessionPool]:
        """Lock a session for a specific test case execution."""
        session = await self.session_repo.lock_session_for_test(suite_id, test_case_id)
        if session:
            logger.info(f"Locked session {session.session_id} for test {test_case_id}")
        else:
            logger.warning(f"No available session to lock for test {test_case_id} in suite {suite_id}")
        return session
    
    async def unlock_session(self, session_id: str) -> bool:
        """Unlock a session after test completion."""
        success = await self.session_repo.unlock_session(session_id)
        if success:
            logger.info(f"Unlocked session: {session_id}")
        return success
    
    async def update_session_health(self, session_id: str, status: str, error: Optional[str] = None) -> bool:
        """Update session health status."""
        return await self.session_repo.update_session_health(session_id, status, error)
    
    async def cleanup_sessions(self) -> Dict[str, int]:
        """Cleanup expired and unhealthy sessions."""
        expired_count = await self.session_repo.cleanup_expired_sessions()
        unhealthy_count = await self.session_repo.cleanup_unhealthy_sessions()
        
        logger.info(f"Cleaned up {expired_count} expired and {unhealthy_count} unhealthy sessions")
        
        return {
            "expired_sessions_cleaned": expired_count,
            "unhealthy_sessions_cleaned": unhealthy_count
        }
    
    async def get_session_stats(self, suite_id: str) -> Dict[str, Any]:
        """Get session statistics for a suite."""
        return await self.session_repo.get_session_stats(suite_id)
    
    async def list_suite_sessions(self, suite_id: str, active_only: bool = True) -> List[BrowserSessionPool]:
        """List all sessions for a suite."""
        return await self.session_repo.get_by_suite(suite_id, active_only)
    
    # Helper methods
    
    def _browser_helper_config_to_dict(self, config: BrowserHelperConfig) -> Dict[str, Any]:
        """Convert BrowserHelperConfig to dictionary."""
        return {
            "headless": config.headless,
            "user_data_dir": config.user_data_dir,
            "allowed_domains": config.allowed_domains,
            "disable_security": config.disable_security,
            "deterministic_rendering": config.deterministic_rendering,
            "highlight_elements": config.highlight_elements,
            "viewport_expansion": config.viewport_expansion,
            "minimum_wait_page_load_time": config.minimum_wait_page_load_time,
            "wait_for_network_idle_page_load_time": config.wait_for_network_idle_page_load_time,
            "maximum_wait_page_load_time": config.maximum_wait_page_load_time,
            "wait_between_actions": config.wait_between_actions,
            "max_steps": config.max_steps,
            "max_failures": config.max_failures,
            "retry_delay": config.retry_delay,
            "use_vision": config.use_vision,
            "generate_gif": config.generate_gif,
            "model_provider": config.model_provider,
            "model_name": config.model_name,
            "temperature": config.temperature,
            "viewport": config.viewport,
            "device_scale_factor": config.device_scale_factor,
            "record_video_dir": config.record_video_dir,
            "trace_path": config.trace_path
        }
    
    async def migrate_legacy_configurations(self, legacy_config_dir: Path) -> int:
        """Migrate legacy JSON configurations to MongoDB."""
        if not legacy_config_dir.exists():
            return 0
        
        migrated_count = 0
        
        for config_file in legacy_config_dir.glob("*.json"):
            try:
                import json
                with open(config_file, 'r', encoding='utf-8') as f:
                    legacy_data = json.load(f)
                
                # Check if already migrated
                existing = await self.config_repo.get_by_config_id(legacy_data.get("config_id", ""))
                if existing:
                    continue
                
                # Create new configuration from legacy data
                config = BrowserConfiguration(
                    config_id=legacy_data.get("config_id"),
                    name=legacy_data.get("name", "Migrated Configuration"),
                    description=legacy_data.get("description"),
                    config_type=legacy_data.get("config_type", "custom"),
                    settings=legacy_data.get("settings", {}),
                    is_default=legacy_data.get("is_default", False),
                    created_at=datetime.fromisoformat(legacy_data.get("created_at", datetime.utcnow().isoformat())),
                    updated_at=datetime.fromisoformat(legacy_data.get("updated_at")) if legacy_data.get("updated_at") else None
                )
                
                await config.save()
                migrated_count += 1
                
                logger.info(f"Migrated legacy configuration: {config.name}")
                
            except Exception as e:
                logger.error(f"Error migrating configuration {config_file}: {str(e)}")
        
        return migrated_count
    
    async def get_configurations_by_execution_type(
        self,
        execution_type: str,
        project_id: Optional[str] = None,
        suite_id: Optional[str] = None,
        include_inactive: bool = False
    ) -> List[BrowserConfiguration]:
        """
        Get configurations that support a specific execution type.
        
        Args:
            execution_type: Type of execution (smoke, full, case, suite, codegen)
            project_id: Optional project ID to filter by
            suite_id: Optional suite ID to filter by
            include_inactive: Whether to include inactive configurations
            
        Returns:
            List of compatible configurations
        """
        try:
            # Build filter criteria
            filters = {
                "execution_types": execution_type,
                "is_active": True if not include_inactive else {"$in": [True, False]}
            }
            
            if project_id:
                filters["$or"] = [
                    {"project_id": project_id},
                    {"project_id": None}  # Include global configurations
                ]
            
            if suite_id:
                filters["$or"] = [
                    {"suite_id": suite_id},
                    {"suite_id": None}  # Include non-suite-specific configurations
                ]
            
            configurations = await self.config_repo.find_many(filters)
            
            logger.info(f"Found {len(configurations)} configurations for execution type: {execution_type}")
            return configurations
            
        except Exception as e:
            logger.error(f"Error getting configurations by execution type: {str(e)}")
            return []
    
    async def update_configuration_execution_types(
        self,
        config_id: str,
        execution_types: List[str]
    ) -> Optional[BrowserConfiguration]:
        """
        Update the execution types that can use a configuration.
        
        Args:
            config_id: Configuration ID
            execution_types: List of execution types
            
        Returns:
            Updated configuration or None if not found
        """
        try:
            config = await self.config_repo.get_by_config_id(config_id)
            if not config:
                logger.warning(f"Configuration not found: {config_id}")
                return None
            
            # Validate execution types
            from ..database.models.browser_configuration import ExecutionType
            valid_types = []
            for exec_type in execution_types:
                try:
                    valid_types.append(ExecutionType(exec_type))
                except ValueError:
                    logger.warning(f"Invalid execution type: {exec_type}")
            
            if not valid_types:
                logger.error("No valid execution types provided")
                return None
            
            # Update configuration
            config.execution_types = valid_types
            config.updated_at = datetime.utcnow()
            
            await config.save()
            
            logger.info(f"Updated execution types for configuration {config_id}: {execution_types}")
            return config
            
        except Exception as e:
            logger.error(f"Error updating configuration execution types: {str(e)}")
            return None
    
    # Abstract methods implementation
    
    async def health_check(self) -> ServiceResult[Dict[str, Any]]:
        """Perform browser configuration service health check."""
        try:
            health_info = {
                "service": self.service_name,
                "status": "healthy",
                "database_enabled": self.should_use_database(),
                "repositories": ["browser_configuration", "browser_session_pool"]
            }
            
            if self.should_use_database():
                db_health = await self.check_database_health()
                health_info["database"] = db_health
                
                # Check basic operations
                try:
                    from ..database.models.browser_configuration import BrowserConfiguration
                    total_configs = await BrowserConfiguration.count()
                    health_info["total_configurations"] = total_configs
                except Exception as e:
                    health_info["database_operations"] = {"error": str(e)}
            
            return ServiceResult.success_result(health_info)
            
        except Exception as e:
            return self.handle_service_error(e, "health_check")
    
    async def get_service_info(self) -> ServiceResult[Dict[str, Any]]:
        """Get browser configuration service information and statistics."""
        try:
            info = {
                "service": self.service_name,
                "description": "Service for managing browser configurations and session pools",
                "version": "1.0.0",
                "features": [
                    "browser_configuration_management",
                    "session_pool_management",
                    "predefined_configurations",
                    "configuration_validation",
                    "session_health_monitoring"
                ]
            }
            
            if self.should_use_database():
                try:
                    from ..database.models.browser_configuration import BrowserConfiguration
                    
                    # Get configuration statistics
                    total_configs = await BrowserConfiguration.count()
                    active_configs = await BrowserConfiguration.find({"is_active": True}).count()
                    
                    info["statistics"] = {
                        "total_configurations": total_configs,
                        "active_configurations": active_configs,
                        "inactive_configurations": total_configs - active_configs
                    }
                    
                    # Get configuration types distribution
                    pipeline = [
                        {"$group": {"_id": "$config_type", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ]
                    config_types = await BrowserConfiguration.aggregate(pipeline).to_list()
                    info["configuration_types"] = {item["_id"]: item["count"] for item in config_types}
                    
                except Exception as e:
                    info["statistics_error"] = str(e)
            
            return ServiceResult.success_result(info)
            
        except Exception as e:
            return self.handle_service_error(e, "get_service_info")